import os
import kagglehub
from pathlib import Path
import matplotlib.pyplot as plt
from PIL import Image
import numpy as np
import random

class SkinCancerDataset:
    """Utility class for working with the Skin Cancer ISIC dataset"""
    
    def __init__(self):
        # Get dataset path
        self.dataset_path = self._get_dataset_path()
        self.train_path = self.dataset_path / "Train"
        self.test_path = self.dataset_path / "Test"
        
        # Get class names
        self.classes = sorted([d.name for d in self.train_path.iterdir() if d.is_dir()])
        self.num_classes = len(self.classes)
        
        print(f"Dataset loaded successfully!")
        print(f"Classes: {self.classes}")
        print(f"Number of classes: {self.num_classes}")
    
    def _get_dataset_path(self):
        """Get the path to the downloaded dataset"""
        # Download if not already downloaded
        path = kagglehub.dataset_download("nodoubttome/skin-cancer9-classesisic")
        dataset_path = Path(path) / "Skin cancer ISIC The International Skin Imaging Collaboration"
        return dataset_path
    
    def get_class_info(self):
        """Get information about each class"""
        info = {}
        for class_name in self.classes:
            train_count = len(list((self.train_path / class_name).glob("*")))
            test_count = len(list((self.test_path / class_name).glob("*")))
            info[class_name] = {
                'train_count': train_count,
                'test_count': test_count,
                'total_count': train_count + test_count
            }
        return info
    
    def show_sample_images(self, class_name=None, num_samples=6, split='train'):
        """Display sample images from the dataset"""
        if class_name is None:
            class_name = random.choice(self.classes)
        
        if split == 'train':
            class_path = self.train_path / class_name
        else:
            class_path = self.test_path / class_name
        
        # Get all image files
        image_files = list(class_path.glob("*.jpg")) + list(class_path.glob("*.jpeg")) + list(class_path.glob("*.png"))
        
        if len(image_files) == 0:
            print(f"No images found in {class_path}")
            return
        
        # Sample random images
        sample_files = random.sample(image_files, min(num_samples, len(image_files)))
        
        # Create subplot
        cols = 3
        rows = (len(sample_files) + cols - 1) // cols
        
        plt.figure(figsize=(15, 5 * rows))
        plt.suptitle(f'Sample images from class: {class_name} ({split} set)', fontsize=16)
        
        for i, img_file in enumerate(sample_files):
            plt.subplot(rows, cols, i + 1)
            
            # Load and display image
            img = Image.open(img_file)
            plt.imshow(img)
            plt.title(f'{img_file.name}\nSize: {img.size}')
            plt.axis('off')
        
        plt.tight_layout()
        plt.show()
    
    def get_image_stats(self, split='train'):
        """Get statistics about image sizes in the dataset"""
        if split == 'train':
            base_path = self.train_path
        else:
            base_path = self.test_path
        
        sizes = []
        for class_name in self.classes:
            class_path = base_path / class_name
            image_files = list(class_path.glob("*.jpg")) + list(class_path.glob("*.jpeg")) + list(class_path.glob("*.png"))
            
            for img_file in image_files[:10]:  # Sample first 10 images per class
                try:
                    with Image.open(img_file) as img:
                        sizes.append(img.size)
                except Exception as e:
                    print(f"Error loading {img_file}: {e}")
        
        if sizes:
            widths = [s[0] for s in sizes]
            heights = [s[1] for s in sizes]
            
            print(f"\nImage size statistics ({split} set):")
            print(f"Width - Min: {min(widths)}, Max: {max(widths)}, Avg: {np.mean(widths):.1f}")
            print(f"Height - Min: {min(heights)}, Max: {max(heights)}, Avg: {np.mean(heights):.1f}")
            print(f"Most common size: {max(set(sizes), key=sizes.count)}")
        
        return sizes
    
    def show_class_distribution(self):
        """Show the distribution of images across classes"""
        info = self.get_class_info()
        
        classes = list(info.keys())
        train_counts = [info[c]['train_count'] for c in classes]
        test_counts = [info[c]['test_count'] for c in classes]
        
        # Create bar plot
        x = np.arange(len(classes))
        width = 0.35
        
        plt.figure(figsize=(15, 8))
        plt.bar(x - width/2, train_counts, width, label='Train', alpha=0.8)
        plt.bar(x + width/2, test_counts, width, label='Test', alpha=0.8)
        
        plt.xlabel('Classes')
        plt.ylabel('Number of Images')
        plt.title('Distribution of Images Across Classes')
        plt.xticks(x, classes, rotation=45, ha='right')
        plt.legend()
        plt.tight_layout()
        plt.show()
        
        # Print summary
        print("\nClass Distribution Summary:")
        print("-" * 50)
        for class_name in classes:
            train_count = info[class_name]['train_count']
            test_count = info[class_name]['test_count']
            total_count = info[class_name]['total_count']
            print(f"{class_name:25}: Train={train_count:3d}, Test={test_count:2d}, Total={total_count:3d}")

# Example usage
if __name__ == "__main__":
    # Create dataset instance
    dataset = SkinCancerDataset()
    
    # Show class distribution
    dataset.show_class_distribution()
    
    # Show sample images from a random class
    dataset.show_sample_images()
    
    # Get image statistics
    dataset.get_image_stats()
