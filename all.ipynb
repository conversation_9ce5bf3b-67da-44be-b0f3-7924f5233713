import kagglehub

# Download latest version
path = kagglehub.dataset_download("nodoubttome/skin-cancer9-classesisic")

print(path)

import os

# Assuming the dataset follows a common structure with 'train' and 'test' folders
train_dir = os.path.join(path, 'train')
test_dir = os.path.join(path, 'test')

print("Train directory:", train_dir)
print("Test directory:", test_dir)

# List the contents of the train directory
print("Files and folders in train_dir:")
if os.path.exists(train_dir):
    for item in os.listdir(train_dir):
        print(item)
else:
    print(f"Directory not found: {train_dir}")

print("Contents of path:", os.listdir(path))

from pathlib import Path
import matplotlib.pyplot as plt
from PIL import Image
import random
import numpy as np

# Set matplotlib to display plots inline
%matplotlib inline

# Navigate to the actual dataset folder
dataset_path = Path(path) / "Skin cancer ISIC The International Skin Imaging Collaboration"

print(f"Dataset structure:")
print(f"Main dataset path: {dataset_path}")
print(f"Dataset path exists: {dataset_path.exists()}")

# Check Train and Test directories
train_path = dataset_path / "Train"
test_path = dataset_path / "Test"

print(f"\nTrain path: {train_path}")
print(f"Train path exists: {train_path.exists()}")
print(f"Test path: {test_path}")
print(f"Test path exists: {test_path.exists()}")

# Get class names
if train_path.exists():
    classes = sorted([d.name for d in train_path.iterdir() if d.is_dir()])
    print(f"Number of classes: {len(classes)}")
    print(f"Classes:")
    for i, class_name in enumerate(classes, 1):
        print(f"  {i}. {class_name}")
else:
    print("Train directory not found!")

# Count images in each class
if train_path.exists():
    train_counts = {}
    test_counts = {}

    print("TRAIN SET:")
    print("-" * 50)
    total_train_images = 0

    for class_name in classes:
        class_path = train_path / class_name
        image_files = [f for f in class_path.iterdir() if f.is_file() and f.suffix.lower() in ['.jpg', '.jpeg', '.png']]
        image_count = len(image_files)
        train_counts[class_name] = image_count
        total_train_images += image_count
        print(f"  {class_name:25}: {image_count:3d} images")

    print(f"\nTotal training images: {total_train_images}")

    print("\nTEST SET:")
    print("-" * 50)
    total_test_images = 0

    for class_name in classes:
        class_path = test_path / class_name
        image_files = [f for f in class_path.iterdir() if f.is_file() and f.suffix.lower() in ['.jpg', '.jpeg', '.png']]
        image_count = len(image_files)
        test_counts[class_name] = image_count
        total_test_images += image_count
        print(f"  {class_name:25}: {image_count:3d} images")

    print(f"\nTotal test images: {total_test_images}")
    print(f"Total dataset images: {total_train_images + total_test_images}")
else:
    print("Cannot count images - train path doesn't exist")

# Visualize the number of training images per class as a bar chart
plt.figure(figsize=(10, 6))
plt.bar(train_counts.keys(), train_counts.values(), color='skyblue')
plt.xticks(rotation=45, ha='right')
plt.xlabel('Class')
plt.ylabel('Number of Training Images')
plt.title('Training Images per Class')
plt.tight_layout()
plt.show()

!pip install tensorflow

#
preprocessed_images = []
if class_path.exists():
    for img_file in image_files:
        img = Image.open(img_file).convert('RGB')
        img_tensor = preprocess(img)
        preprocessed_images.append(img_tensor)
    print(f"Preprocessed {len(preprocessed_images)} images for class '{class_name}'.")
else:
    print(f"Class path does not exist: {class_path}")





# تثبيت المكتبات المطلوبة
!pip install tensorflow scikit-learn opencv-python seaborn

import tensorflow as tf
from tensorflow.keras import layers, models
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.applications import EfficientNetB0
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import classification_report, confusion_matrix
import cv2
from PIL import Image
import seaborn as sns

print(f"TensorFlow version: {tf.__version__}")
print(f"GPU available: {len(tf.config.list_physical_devices('GPU')) > 0}")

# إعدادات النموذج
IMG_SIZE = 224
BATCH_SIZE = 32
EPOCHS = 25

# مولد بيانات التدريب مع تحسين الصور (Data Augmentation)
train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=30,
    width_shift_range=0.2,
    height_shift_range=0.2,
    shear_range=0.2,
    zoom_range=0.2,
    horizontal_flip=True,
    vertical_flip=True,
    fill_mode='nearest',
    validation_split=0.2  # استخدام 20% للتحقق
)

# مولد بيانات الاختبار (بدون تحسين)
test_datagen = ImageDataGenerator(rescale=1./255)

# إنشاء مولدات البيانات
train_generator = train_datagen.flow_from_directory(
    train_path,
    target_size=(IMG_SIZE, IMG_SIZE),
    batch_size=BATCH_SIZE,
    class_mode='categorical',
    subset='training'
)

validation_generator = train_datagen.flow_from_directory(
    train_path,
    target_size=(IMG_SIZE, IMG_SIZE),
    batch_size=BATCH_SIZE,
    class_mode='categorical',
    subset='validation'
)

test_generator = test_datagen.flow_from_directory(
    test_path,
    target_size=(IMG_SIZE, IMG_SIZE),
    batch_size=BATCH_SIZE,
    class_mode='categorical',
    shuffle=False
)

print(f"عدد فئات السرطان: {train_generator.num_classes}")
print(f"أسماء الفئات: {list(train_generator.class_indices.keys())}")
print(f"عدد صور التدريب: {train_generator.samples}")
print(f"عدد صور التحقق: {validation_generator.samples}")
print(f"عدد صور الاختبار: {test_generator.samples}")

def create_skin_cancer_model(num_classes):
    """إنشاء نموذج لتشخيص سرطان الجلد باستخدام EfficientNet"""
    
    # استخدام EfficientNetB0 كنموذج أساسي
    base_model = EfficientNetB0(
        weights='imagenet',
        include_top=False,
        input_shape=(IMG_SIZE, IMG_SIZE, 3)
    )
    
    # تجميد طبقات النموذج الأساسي في البداية
    base_model.trainable = False
    
    # بناء النموذج الكامل
    model = models.Sequential([
        base_model,
        layers.GlobalAveragePooling2D(),
        layers.Dropout(0.3),
        layers.Dense(512, activation='relu'),
        layers.BatchNormalization(),
        layers.Dropout(0.5),
        layers.Dense(256, activation='relu'),
        layers.Dropout(0.3),
        layers.Dense(num_classes, activation='softmax')
    ])
    
    return model

# إنشاء النموذج
model = create_skin_cancer_model(train_generator.num_classes)

# عرض هيكل النموذج
model.summary()

# تجميع النموذج
model.compile(
    optimizer=Adam(learning_rate=0.001),
    loss='categorical_crossentropy',
    metrics=['accuracy', 'top_3_accuracy']
)

print("\n✅ تم إنشاء النموذج بنجاح!")

# إعداد callbacks للتحكم في التدريب
callbacks = [
    EarlyStopping(
        monitor='val_accuracy',
        patience=5,
        restore_best_weights=True,
        verbose=1
    ),
    ReduceLROnPlateau(
        monitor='val_loss',
        factor=0.2,
        patience=3,
        min_lr=1e-7,
        verbose=1
    )
]

print("🚀 بدء تدريب النموذج...")
print(f"عدد العصور: {EPOCHS}")
print(f"حجم الدفعة: {BATCH_SIZE}")

# تدريب النموذج
history = model.fit(
    train_generator,
    epochs=EPOCHS,
    validation_data=validation_generator,
    callbacks=callbacks,
    verbose=1
)

print("\n✅ انتهى التدريب!")

# رسم منحنيات التدريب
def plot_training_history(history):
    """رسم منحنيات الدقة والخسارة أثناء التدريب"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
    
    # رسم الدقة
    ax1.plot(history.history['accuracy'], label='Training Accuracy', linewidth=2)
    ax1.plot(history.history['val_accuracy'], label='Validation Accuracy', linewidth=2)
    ax1.set_title('دقة النموذج أثناء التدريب', fontsize=14, fontweight='bold')
    ax1.set_xlabel('العصر (Epoch)')
    ax1.set_ylabel('الدقة (Accuracy)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # رسم الخسارة
    ax2.plot(history.history['loss'], label='Training Loss', linewidth=2)
    ax2.plot(history.history['val_loss'], label='Validation Loss', linewidth=2)
    ax2.set_title('خسارة النموذج أثناء التدريب', fontsize=14, fontweight='bold')
    ax2.set_xlabel('العصر (Epoch)')
    ax2.set_ylabel('الخسارة (Loss)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # طباعة أفضل النتائج
    best_val_acc = max(history.history['val_accuracy'])
    best_val_loss = min(history.history['val_loss'])
    
    print(f"\n📊 أفضل النتائج:")
    print(f"أفضل دقة تحقق: {best_val_acc:.4f}")
    print(f"أقل خسارة تحقق: {best_val_loss:.4f}")

# عرض نتائج التدريب
plot_training_history(history)

# تقييم النموذج على بيانات الاختبار
print("🧪 تقييم النموذج على بيانات الاختبار...")

test_loss, test_accuracy, test_top3_accuracy = model.evaluate(test_generator, verbose=1)

print(f"\n📊 نتائج الاختبار:")
print(f"دقة الاختبار: {test_accuracy:.4f} ({test_accuracy*100:.2f}%)")
print(f"دقة Top-3: {test_top3_accuracy:.4f} ({test_top3_accuracy*100:.2f}%)")
print(f"خسارة الاختبار: {test_loss:.4f}")

# التنبؤ على بيانات الاختبار
test_generator.reset()
predictions = model.predict(test_generator, verbose=1)
predicted_classes = np.argmax(predictions, axis=1)

# الحصول على الفئات الحقيقية
true_classes = test_generator.classes
class_labels = list(test_generator.class_indices.keys())

# تقرير التصنيف
print("\n📋 تقرير التصنيف التفصيلي:")
print(classification_report(true_classes, predicted_classes, target_names=class_labels))

# رسم مصفوفة الخلط
def plot_confusion_matrix(true_classes, predicted_classes, class_labels):
    """رسم مصفوفة الخلط"""
    
    cm = confusion_matrix(true_classes, predicted_classes)
    
    plt.figure(figsize=(12, 10))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=class_labels, yticklabels=class_labels)
    plt.title('مصفوفة الخلط - نتائج التصنيف', fontsize=16, fontweight='bold')
    plt.xlabel('التصنيف المتوقع')
    plt.ylabel('التصنيف الحقيقي')
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.show()
    
    # حساب الدقة لكل فئة
    class_accuracy = cm.diagonal() / cm.sum(axis=1)
    
    print("\n📊 دقة التصنيف لكل فئة:")
    for i, (class_name, accuracy) in enumerate(zip(class_labels, class_accuracy)):
        print(f"{class_name:25}: {accuracy:.4f} ({accuracy*100:.2f}%)")

# رسم مصفوفة الخلط
plot_confusion_matrix(true_classes, predicted_classes, class_labels)

# حفظ النموذج
model_save_path = 'skin_cancer_classifier_model.h5'
model.save(model_save_path)
print(f"✅ تم حفظ النموذج في: {model_save_path}")

# حفظ أسماء الفئات
import json

class_names_dict = {v: k for k, v in train_generator.class_indices.items()}
with open('class_names.json', 'w', encoding='utf-8') as f:
    json.dump(class_names_dict, f, ensure_ascii=False, indent=2)

print("✅ تم حفظ أسماء الفئات في: class_names.json")

def predict_skin_cancer(image_path, model, class_names_dict, img_size=224):
    """التنبؤ بنوع سرطان الجلد من صورة"""
    
    try:
        # تحميل وتحضير الصورة
        img = Image.open(image_path).convert('RGB')
        img = img.resize((img_size, img_size))
        img_array = np.array(img) / 255.0
        img_array = np.expand_dims(img_array, axis=0)
        
        # التنبؤ
        predictions = model.predict(img_array, verbose=0)
        predicted_class_idx = np.argmax(predictions[0])
        confidence = predictions[0][predicted_class_idx]
        
        # الحصول على اسم الفئة
        predicted_class_name = class_names_dict[predicted_class_idx]
        
        # عرض النتيجة
        plt.figure(figsize=(10, 6))
        
        # عرض الصورة
        plt.subplot(1, 2, 1)
        plt.imshow(img)
        plt.title('الصورة المدخلة', fontsize=14)
        plt.axis('off')
        
        # عرض التنبؤات
        plt.subplot(1, 2, 2)
        top_5_indices = np.argsort(predictions[0])[-5:][::-1]
        top_5_probs = predictions[0][top_5_indices]
        top_5_classes = [class_names_dict[i] for i in top_5_indices]
        
        colors = ['red' if i == 0 else 'skyblue' for i in range(5)]
        bars = plt.barh(range(5), top_5_probs, color=colors)
        plt.yticks(range(5), top_5_classes)
        plt.xlabel('احتمالية التنبؤ')
        plt.title('أعلى 5 تنبؤات', fontsize=14)
        plt.gca().invert_yaxis()
        
        # إضافة النسب المئوية
        for i, (bar, prob) in enumerate(zip(bars, top_5_probs)):
            plt.text(bar.get_width() + 0.01, bar.get_y() + bar.get_height()/2, 
                    f'{prob:.3f} ({prob*100:.1f}%)', 
                    va='center', fontsize=10)
        
        plt.tight_layout()
        plt.show()
        
        # طباعة النتيجة
        print(f"\n🎯 نتيجة التشخيص:")
        print(f"النوع المتوقع: {predicted_class_name}")
        print(f"مستوى الثقة: {confidence:.4f} ({confidence*100:.2f}%)")
        
        if confidence > 0.7:
            print("✅ مستوى ثقة عالي")
        elif confidence > 0.5:
            print("⚠️ مستوى ثقة متوسط")
        else:
            print("❌ مستوى ثقة منخفض")
            
        return predicted_class_name, confidence
        
    except Exception as e:
        print(f"❌ خطأ في معالجة الصورة: {e}")
        return None, None

# مثال على الاستخدام (قم بتغيير مسار الصورة)
print("📝 لاختبار النموذج على صورة جديدة، استخدم الكود التالي:")
print("predict_skin_cancer('path_to_your_image.jpg', model, class_names_dict)")

# يمكنك اختبار النموذج على صورة من بيانات الاختبار
# احصل على صورة عشوائية من بيانات الاختبار
import random
import glob

# البحث عن صورة عشوائية من بيانات الاختبار
test_images = []
for class_name in class_labels:
    class_test_path = test_path / class_name
    images = list(class_test_path.glob('*.jpg')) + list(class_test_path.glob('*.jpeg')) + list(class_test_path.glob('*.png'))
    test_images.extend(images)

if test_images:
    # اختيار صورة عشوائية
    random_image = random.choice(test_images)
    print(f"\n🔍 اختبار النموذج على صورة عشوائية: {random_image.name}")
    print(f"الفئة الحقيقية: {random_image.parent.name}")
    
    # التنبؤ
    predicted_class, confidence = predict_skin_cancer(str(random_image), model, class_names_dict)
else:
    print("لم يتم العثور على صور للاختبار")

print("🏥 ملخص نموذج تشخيص سرطان الجلد")
print("=" * 50)
print(f"📊 عدد فئات السرطان: {train_generator.num_classes}")
print(f"🖼️  حجم الصورة المستخدم: {IMG_SIZE}x{IMG_SIZE}")
print(f"📦 حجم الدفعة: {BATCH_SIZE}")
print(f"🔄 عدد العصور: {len(history.history['accuracy'])}")
print(f"🎯 دقة الاختبار النهائية: {test_accuracy:.4f} ({test_accuracy*100:.2f}%)")
print(f"📈 دقة Top-3: {test_top3_accuracy:.4f} ({test_top3_accuracy*100:.2f}%)")

print(f"\n🏷️  فئات السرطان المدعومة:")
for i, class_name in enumerate(class_labels, 1):
    print(f"  {i:2d}. {class_name}")

print(f"\n💾 ملفات النموذج المحفوظة:")
print(f"  - النموذج: {model_save_path}")
print(f"  - أسماء الفئات: class_names.json")

print(f"\n⚠️  تنبيه طبي مهم:")
print(f"هذا النموذج مخصص للأغراض التعليمية والبحثية فقط.")
print(f"لا يجب استخدامه كبديل للتشخيص الطبي المتخصص.")
print(f"استشر طبيب الأمراض الجلدية دائماً للحصول على تشخيص دقيق.")

print(f"\n✅ تم إنجاز المشروع بنجاح! 🎉")