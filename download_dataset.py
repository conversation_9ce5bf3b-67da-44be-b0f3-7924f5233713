import kagglehub
import os
from pathlib import Path

# Download latest version
path = kagglehub.dataset_download("nodoubttome/skin-cancer9-classesisic")

print("Path to dataset files:", path)
print("\n" + "="*60)
print("DATASET EXPLORATION")
print("="*60)

# Navigate to the actual dataset folder
dataset_path = Path(path) / "Skin cancer ISIC The International Skin Imaging Collaboration"

if dataset_path.exists():
    print(f"\nDataset structure:")
    print(f"Main dataset path: {dataset_path}")

    # Check Train and Test directories
    train_path = dataset_path / "Train"
    test_path = dataset_path / "Test"

    if train_path.exists():
        print(f"\nTRAIN SET:")
        print("-" * 40)
        train_classes = [d.name for d in train_path.iterdir() if d.is_dir()]
        train_classes.sort()

        total_train_images = 0
        for class_name in train_classes:
            class_path = train_path / class_name
            image_count = len([f for f in class_path.iterdir() if f.is_file() and f.suffix.lower() in ['.jpg', '.jpeg', '.png']])
            total_train_images += image_count
            print(f"  {class_name}: {image_count} images")

        print(f"\nTotal training images: {total_train_images}")

    if test_path.exists():
        print(f"\nTEST SET:")
        print("-" * 40)
        test_classes = [d.name for d in test_path.iterdir() if d.is_dir()]
        test_classes.sort()

        total_test_images = 0
        for class_name in test_classes:
            class_path = test_path / class_name
            image_count = len([f for f in class_path.iterdir() if f.is_file() and f.suffix.lower() in ['.jpg', '.jpeg', '.png']])
            total_test_images += image_count
            print(f"  {class_name}: {image_count} images")

        print(f"\nTotal test images: {total_test_images}")
        print(f"Total dataset images: {total_train_images + total_test_images}")

    print(f"\nSkin Cancer Classes ({len(train_classes)} classes):")
    print("-" * 40)
    for i, class_name in enumerate(train_classes, 1):
        print(f"{i}. {class_name}")

else:
    print("Dataset path not found. Please check the download.")
