"""
Example script showing how to use the Skin Cancer ISIC dataset
"""

from dataset_utils import SkinCancerDataset
import matplotlib.pyplot as plt

def main():
    print("🔬 Skin Cancer ISIC Dataset Explorer")
    print("=" * 50)
    
    # Initialize the dataset
    dataset = SkinCancerDataset()
    
    print(f"\n📊 Dataset Overview:")
    print(f"Number of classes: {dataset.num_classes}")
    print(f"Classes: {', '.join(dataset.classes)}")
    
    # Show class distribution
    print(f"\n📈 Showing class distribution...")
    dataset.show_class_distribution()
    
    # Show sample images from different classes
    print(f"\n🖼️  Showing sample images...")
    
    # Show samples from a few interesting classes
    interesting_classes = ['melanoma', 'basal cell carcinoma', 'nevus']
    
    for class_name in interesting_classes:
        if class_name in dataset.classes:
            print(f"\nShowing samples from: {class_name}")
            dataset.show_sample_images(class_name=class_name, num_samples=6)
    
    # Get image statistics
    print(f"\n📏 Getting image statistics...")
    train_sizes = dataset.get_image_stats(split='train')
    test_sizes = dataset.get_image_stats(split='test')
    
    print(f"\n✅ Dataset exploration complete!")
    print(f"You can now use this dataset for:")
    print(f"  • Machine learning model training")
    print(f"  • Computer vision research")
    print(f"  • Skin cancer classification")
    print(f"  • Medical image analysis")

if __name__ == "__main__":
    main()
