"""
Machine Learning Example using the Skin Cancer ISIC Dataset
This example shows how to prepare the data for training a CNN model
"""

import os
import numpy as np
from pathlib import Path
from PIL import Image
import matplotlib.pyplot as plt
from dataset_utils import SkinCancerDataset

def load_and_preprocess_images(dataset, split='train', target_size=(224, 224), max_images_per_class=50):
    """
    Load and preprocess images for machine learning
    
    Args:
        dataset: SkinCancerDataset instance
        split: 'train' or 'test'
        target_size: tuple, target image size (width, height)
        max_images_per_class: int, maximum number of images to load per class
    
    Returns:
        X: numpy array of preprocessed images
        y: numpy array of labels
        class_names: list of class names
    """
    
    if split == 'train':
        base_path = dataset.train_path
    else:
        base_path = dataset.test_path
    
    images = []
    labels = []
    class_names = dataset.classes
    
    print(f"Loading {split} images...")
    
    for class_idx, class_name in enumerate(class_names):
        class_path = base_path / class_name
        image_files = list(class_path.glob("*.jpg")) + list(class_path.glob("*.jpeg")) + list(class_path.glob("*.png"))
        
        # Limit number of images per class
        image_files = image_files[:max_images_per_class]
        
        print(f"  Loading {len(image_files)} images from {class_name}...")
        
        for img_file in image_files:
            try:
                # Load and resize image
                img = Image.open(img_file)
                img = img.convert('RGB')  # Ensure RGB format
                img = img.resize(target_size)
                
                # Convert to numpy array and normalize
                img_array = np.array(img) / 255.0
                
                images.append(img_array)
                labels.append(class_idx)
                
            except Exception as e:
                print(f"    Error loading {img_file}: {e}")
    
    X = np.array(images)
    y = np.array(labels)
    
    print(f"Loaded {len(X)} images with shape {X.shape}")
    print(f"Labels shape: {y.shape}")
    
    return X, y, class_names

def show_sample_batch(X, y, class_names, num_samples=9):
    """Show a sample batch of preprocessed images"""
    
    # Select random samples
    indices = np.random.choice(len(X), min(num_samples, len(X)), replace=False)
    
    # Create subplot
    cols = 3
    rows = (len(indices) + cols - 1) // cols
    
    plt.figure(figsize=(12, 4 * rows))
    plt.suptitle('Sample Preprocessed Images', fontsize=16)
    
    for i, idx in enumerate(indices):
        plt.subplot(rows, cols, i + 1)
        plt.imshow(X[idx])
        plt.title(f'Class: {class_names[y[idx]]}')
        plt.axis('off')
    
    plt.tight_layout()
    plt.show()

def analyze_class_distribution(y, class_names):
    """Analyze and visualize class distribution"""
    
    unique, counts = np.unique(y, return_counts=True)
    
    plt.figure(figsize=(12, 6))
    bars = plt.bar(range(len(unique)), counts)
    plt.xlabel('Classes')
    plt.ylabel('Number of Images')
    plt.title('Class Distribution in Loaded Dataset')
    plt.xticks(range(len(unique)), [class_names[i] for i in unique], rotation=45, ha='right')
    
    # Add count labels on bars
    for bar, count in zip(bars, counts):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, 
                str(count), ha='center', va='bottom')
    
    plt.tight_layout()
    plt.show()
    
    print("\nClass distribution:")
    for i, count in zip(unique, counts):
        print(f"  {class_names[i]}: {count} images")

def create_tensorflow_dataset_example():
    """
    Example of how to create a TensorFlow dataset (requires tensorflow)
    This is just a template - uncomment and modify as needed
    """
    
    example_code = '''
    # Example TensorFlow dataset creation (requires: pip install tensorflow)
    
    import tensorflow as tf
    from sklearn.model_selection import train_test_split
    from sklearn.preprocessing import LabelEncoder
    
    # Load data
    dataset = SkinCancerDataset()
    X_train, y_train, class_names = load_and_preprocess_images(dataset, 'train')
    X_test, y_test, _ = load_and_preprocess_images(dataset, 'test')
    
    # Convert labels to categorical
    num_classes = len(class_names)
    y_train_cat = tf.keras.utils.to_categorical(y_train, num_classes)
    y_test_cat = tf.keras.utils.to_categorical(y_test, num_classes)
    
    # Create TensorFlow datasets
    train_dataset = tf.data.Dataset.from_tensor_slices((X_train, y_train_cat))
    test_dataset = tf.data.Dataset.from_tensor_slices((X_test, y_test_cat))
    
    # Add data augmentation and batching
    train_dataset = train_dataset.shuffle(1000).batch(32).prefetch(tf.data.AUTOTUNE)
    test_dataset = test_dataset.batch(32).prefetch(tf.data.AUTOTUNE)
    
    # Simple CNN model
    model = tf.keras.Sequential([
        tf.keras.layers.Conv2D(32, 3, activation='relu', input_shape=(224, 224, 3)),
        tf.keras.layers.MaxPooling2D(),
        tf.keras.layers.Conv2D(64, 3, activation='relu'),
        tf.keras.layers.MaxPooling2D(),
        tf.keras.layers.Conv2D(64, 3, activation='relu'),
        tf.keras.layers.Flatten(),
        tf.keras.layers.Dense(64, activation='relu'),
        tf.keras.layers.Dense(num_classes, activation='softmax')
    ])
    
    model.compile(optimizer='adam',
                  loss='categorical_crossentropy',
                  metrics=['accuracy'])
    
    # Train the model
    history = model.fit(train_dataset, epochs=10, validation_data=test_dataset)
    '''
    
    print("TensorFlow Example Code:")
    print("=" * 50)
    print(example_code)

def main():
    print("🤖 Machine Learning Example with Skin Cancer Dataset")
    print("=" * 60)
    
    # Initialize dataset
    dataset = SkinCancerDataset()
    
    # Load and preprocess training data (limited for demo)
    print("\n📥 Loading training data...")
    X_train, y_train, class_names = load_and_preprocess_images(
        dataset, 'train', target_size=(224, 224), max_images_per_class=20
    )
    
    # Load test data
    print("\n📥 Loading test data...")
    X_test, y_test, _ = load_and_preprocess_images(
        dataset, 'test', target_size=(224, 224), max_images_per_class=10
    )
    
    # Show sample images
    print("\n🖼️  Showing sample preprocessed images...")
    show_sample_batch(X_train, y_train, class_names)
    
    # Analyze class distribution
    print("\n📊 Analyzing class distribution...")
    analyze_class_distribution(y_train, class_names)
    
    # Show TensorFlow example
    print("\n🧠 TensorFlow Integration Example:")
    create_tensorflow_dataset_example()
    
    print("\n✅ ML example complete!")
    print("Next steps:")
    print("  1. Install TensorFlow: pip install tensorflow")
    print("  2. Use the example code above to train a CNN model")
    print("  3. Experiment with data augmentation and different architectures")
    print("  4. Evaluate model performance on the test set")

if __name__ == "__main__":
    main()
